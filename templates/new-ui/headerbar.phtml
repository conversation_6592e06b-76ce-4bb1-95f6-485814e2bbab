<?php
$accountId = $this->session->accountId;
$userAccessRole = $this->loggedUser->getMyfootRole();
$userIsAdmin = ($userAccessRole == Genesis_Entity_UserAccess::ROLE_GOD
    || $userAccessRole == Genesis_Entity_UserAccess::ROLE_ADMIN);
?>
<div id="mobile-header" class="ui fixed main menu header-bar">
    <a class="js-toggle-menu toggle-menu ui button basic">
        <i class="icon sidebar"></i>
        <label>Menu</label>
    </a>
    <div class="js-account-settings account-settings ui dropdown button basic">
        <i class="user icon"></i>
        <label>My Account</label>
        <div class="menu">
            <a class="item" href="<?=$this->url(['action'=> 'about'], 'user')?>?account_id=<?=$accountId?>">
                <div style="margin-bottom:10px">
                    <?=$this->loggedUser->getEmail()?>
                </div>
                <strong>Access Level:</strong>
                <?=$userAccessRole?>
                <br/>
                <strong>Account ID:</strong>
                <?=$this->loggedUser->getAccount()->getSfAccountId()?>
            </a>
            <div class="divider"></div>
            <a class="item" href="<?=$this->url([], 'settings')?>?account_id=<?=$accountId?>">
                <i class="info icon"></i>
                My Information</a>
    <?php if ($userIsAdmin) { ?>
            <a class="item" href="<?=$this->url(['action'=>'corporate'], 'settings')?>?account_id=<?=$accountId?>">
                <i class="book icon"></i>
                Corporate Account</a>
            <a class="item" href="<?=$this->url([], 'user')?>?account_id=<?=$accountId?>">
                <i class="male icon"></i>
                User Management</a>
    <?php } ?>
            <div class="divider"></div>
            <a class="item" target="_blank" href="https://support.sparefoot.com/hc/en-us">
                <i class="help icon"></i> Help
            </a>
            <a class="item" href="<?=$this->url(['action'=> 'logout'], 'login')?>">
                <i class="sign out icon"></i> Log Out
            </a>
        </div>
    </div>
    <a class="logo" href="/?account_id=<?=$accountId?>"><h1>MySpareFoot</h1></a>
</div>
<nav id="site-header" class="ui fixed main menu header-bar">
    <div class="container">
        <?php if ($this->loggedUser->getAccount()->getNumFacilities()): ?>
            <div class="js-facility-header item left borderless title-container hidden">
                <div id="site-fac-dropdown" class="ui dropdown search hidden">
                    <div id="site-fac-dropdown-default-text" class="text default"></div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <div class="items"></div>
                        <?php if ($userIsAdmin) { ?>
                        <div class="button-row">
                            <a href="/features/type?account_id=<?=$accountId?>" class="ui primary button fluid" id="add-facility">Add Facility</a>
                        </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
                <div class="js-account-header item left borderless title-container hidden">
                    <div id="site-fac-dropdown">
                        <div class="text">
                            <?= $this->loggedUser->getAccount()->getName() ?>
                        </div>
                    </div>
                </div>
                <div id="mobile-sidebar-button" class="ui button basic">
                    <i class="icon sidebar"></i> Menu
                </div>
                <div class="right menu">
                    <div id="user">
                        <div class="ui dropdown item">
                            <i class="user icon"></i>
                            <label>My Account</label>
                            <i class="dropdown icon"></i>
                            <div class="menu">
                                <a class="item" href="<?=$this->url(['action'=> 'about'], 'user')?>?account_id=<?=$accountId?>">
                                    <div style="margin-bottom:10px">
                                        <?=$this->loggedUser->getEmail()?>
                                    </div>
                                    <strong>Access Level:</strong>
                                    <?=$userAccessRole?>
                                    <br/>
                                    <strong>Account ID:</strong>
                                    <?=$this->loggedUser->getAccount()->getSfAccountId()?>
                                </a>
                                <div class="divider"></div>
                                <a class="item" href="<?=$this->url([], 'settings')?>?account_id=<?=$accountId?>">
                                    <i class="info icon"></i>
                                    My Information</a>
                            <?php if ($userIsAdmin) { ?>
                                <a class="item" href="<?=$this->url(['action'=>'corporate'], 'settings')?>?account_id=<?=$accountId?>">
                                    <i class="book icon"></i>
                                    Corporate Account</a>
                                <a class="item" href="<?=$this->url([], 'user')?>?account_id=<?=$accountId?>">
                                    <i class="male icon"></i>
                                    User Management</a>
                                <a class="item" href="<?=$this->url([], 'payment')?>">
                                    <i class="payment icon"></i>
                                    Payment Information</a>
                            <?php } ?>
                                <div class="divider"></div>
                                <a class="item" target="_blank" href="https://support.sparefoot.com/hc/en-us">
                                    <i class="help icon"></i> Help
                                </a>
                                <a class="item qa-header-logout" href="<?=$this->url(['action'=> 'logout'], 'login')?>">
                                    <i class="sign out icon"></i> Log Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right menu">
                    <div id="contact-us-button" class="item">
                        <img src="/images/icons/contact-icon.svg" class="contact-icon icon">
                        <span class="contact-us">Contact Us Now</span>
                    </div>
                </div>
                <?php if ($userIsAdmin) { ?>
                    <div class="add-facility-btn right borderless item hidden">
                        <a href="/features/type?account_id=<?=$accountId?>" class="btn-site-header ui blue button basic icon">
                            <i class="plus icon"></i> Add Facility
                        </a>
                    </div>
                <?php } ?>
    </div>
</nav>
<div id="nav-spacer"></div>
<div id="contact-us-modal" class="ui small modal">
    <div class="header">
        <img src="/images/icons/contact-icon.svg" class="contact-icon">
        <h2 class="modal-title">Contact us if you need help or have questions!</h2>
        <i class="close icon"></i>
    </div>
    <div class="description">
        <p class="modal-text">Email us: <EMAIL></p>
        <p class="modal-text">Call us: (855)- 427-8193 x 2</p>
        <h2 class="modal-subtitle">Regular Support Hours</h2>
        <p class="modal-text">Monday- Friday</p>
        <p class="modal-text">9AM - 6PM EST</p>
    </div>
</div>
